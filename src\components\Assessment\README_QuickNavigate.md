# Quick Navigate Feature

## Overview
Fitur Quick Navigate memungkinkan user untuk navigasi langsung ke soal-soal spesifik dalam kategori yang sedang aktif di assessment sidebar.

## Features

### 1. Compact Sidebar Design
- Sidebar width dikurangi dari 320px (w-80) menjadi 288px (w-72)
- Padding dikurangi dari 24px (p-6) menjadi 12px (p-3)
- Font sizes dikurangi untuk menghemat space
- Phase cards padding dikurangi dari 16px (p-4) menjadi 12px (p-3)

### 2. Quick Navigate Buttons
- Muncul ketika user berada di kategori tertentu
- Layout grid 5 kolom dengan row yang menyesuaikan jumlah soal
- Setiap button menampilkan nomor soal (1, 2, 3, dst.)
- Visual indicators:
  - **Current question**: Background hitam dengan text putih
  - **Answered questions**: Background hijau muda dengan text hijau tua
  - **Unanswered questions**: Background putih dengan text abu-abu

### 3. Enhanced Category Display
- Progress indicator dengan background color yang berbeda:
  - **Current category**: Background hitam dengan text putih
  - **Completed category**: Background hijau muda dengan text hijau
  - **In-progress category**: Background biru muda dengan text biru
  - **Not started category**: Background putih dengan text abu-abu
- Progress counter dengan styling yang lebih baik

## Implementation Details

### New Props in AssessmentSidebar
```jsx
currentQuestionIndex = 0, // Index soal yang sedang aktif
onNavigateToQuestion // Callback function untuk navigasi ke soal spesifik
```

### New State in AssessmentForm
```jsx
const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
```

### Navigation Function
```jsx
const handleNavigateToQuestion = (questionIndex) => {
  setCurrentQuestionIndex(questionIndex);
  // Scroll to specific question with smooth animation
  setTimeout(() => {
    const questionElement = document.querySelector(`[data-question-index="${questionIndex}"]`);
    if (questionElement) {
      questionElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, 100);
};
```

### Question Wrapper
Setiap soal dibungkus dengan div yang memiliki `data-question-index` untuk memudahkan scroll targeting:
```jsx
<div key={q.questionKey} data-question-index={index}>
  <AssessmentQuestion ... />
</div>
```

## Usage Example

1. User membuka assessment (VIA, RIASEC, atau Big Five)
2. Pilih kategori di sidebar (misal: "Wisdom and Knowledge" di VIA)
3. Quick navigate buttons muncul di bawah kategori yang aktif
4. User dapat klik nomor soal (1-20) untuk langsung jump ke soal tersebut
5. Visual feedback menunjukkan soal mana yang sudah dijawab dan mana yang sedang aktif

## Benefits

1. **Space Efficiency**: Sidebar lebih compact, memberikan lebih banyak ruang untuk konten utama
2. **Better Navigation**: User dapat langsung jump ke soal spesifik tanpa scroll manual
3. **Visual Feedback**: Clear indication of progress dan current position
4. **Improved UX**: Faster navigation especially untuk assessment dengan banyak soal (VIA = 96 soal)

## Technical Notes

- Quick navigate hanya muncul untuk kategori yang sedang aktif
- Button layout menggunakan CSS Grid dengan 5 kolom
- Smooth scrolling dengan fallback ke scroll top jika element tidak ditemukan
- State management terintegrasi dengan navigation system yang sudah ada
