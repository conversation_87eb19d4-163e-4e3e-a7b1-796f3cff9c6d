import { ChevronRight } from 'lucide-react';

// Quick Navigate Buttons Component
const QuickNavigateButtons = ({
  categoryKey,
  categoryData,
  answers,
  currentQuestionIndex,
  onNavigateToQuestion
}) => {
  // Get all questions for the category
  const allQuestions = [];

  // Regular questions
  categoryData.questions.forEach((_, index) => {
    allQuestions.push({
      questionKey: `${categoryKey}_${index}`,
      index: index,
      isReverse: false
    });
  });

  // Reverse questions (for Big Five)
  if (categoryData.reverseQuestions) {
    categoryData.reverseQuestions.forEach((_, index) => {
      allQuestions.push({
        questionKey: `${categoryKey}_reverse_${index}`,
        index: categoryData.questions.length + index,
        isReverse: true
      });
    });
  }

  return (
    <div className="mt-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
      <h5 className="text-xs font-medium text-gray-700 mb-2">Quick Navigate</h5>
      <div className="grid grid-cols-5 gap-1">
        {allQuestions.map((q, index) => {
          const isAnswered = answers[q.questionKey] !== undefined;
          const isCurrent = currentQuestionIndex === index;

          return (
            <button
              key={q.questionKey}
              onClick={() => onNavigateToQuestion(index)}
              className={`
                h-8 w-8 text-xs font-medium rounded border transition-all duration-200
                ${isCurrent
                  ? 'bg-gray-900 text-white border-gray-900'
                  : isAnswered
                    ? 'bg-green-100 text-green-800 border-green-300 hover:bg-green-200'
                    : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-100'
                }
              `}
            >
              {index + 1}
            </button>
          );
        })}
      </div>
    </div>
  );
};

const AssessmentSidebar = ({
  assessmentData,
  answers,
  currentPage,
  setCurrentPage,
  currentStep,
  totalSteps,
  onFillRandomAnswers,
  onFillAllAssessments,
  onNavigateToPhase, // New prop for phase navigation
  currentQuestionIndex = 0, // New prop for current question index
  onNavigateToQuestion // New prop for question navigation
}) => {
  // Define assessment phases mapping (ordered to match actual flow)
  const assessmentPhases = [
    {
      id: 1,
      title: "Phase 1",
      subtitle: "VIAIS Character Strengths",
      assessmentKey: "via",
      step: 1, // VIA is step 1 in the flow
      totalQuestions: 96
    },
    {
      id: 2,
      title: "Phase 2",
      subtitle: "RIASEC Holland Codes",
      assessmentKey: "riasec",
      step: 2, // RIASEC is step 2 in the flow
      totalQuestions: 60
    },
    {
      id: 3,
      title: "Phase 3",
      subtitle: "OCEAN Personality",
      assessmentKey: "bigFive",
      step: 3, // Big Five is step 3 in the flow
      totalQuestions: 44
    }
  ];

  // Calculate category progress
  const getCategoryProgress = (categoryKey) => {
    const category = assessmentData.categories[categoryKey];
    if (!category) return { answered: 0, total: 0 };
    
    let total = category.questions.length;
    if (category.reverseQuestions) {
      total += category.reverseQuestions.length;
    }
    
    let answered = 0;
    // Count regular questions
    category.questions.forEach((_, index) => {
      const questionKey = `${categoryKey}_${index}`;
      if (answers[questionKey] !== undefined) answered++;
    });
    
    // Count reverse questions
    if (category.reverseQuestions) {
      category.reverseQuestions.forEach((_, index) => {
        const questionKey = `${categoryKey}_reverse_${index}`;
        if (answers[questionKey] !== undefined) answered++;
      });
    }
    
    return { answered, total };
  };

  // Calculate total progress for current assessment
  const getTotalProgress = () => {
    const allQuestions = [];
    Object.entries(assessmentData.categories).forEach(([categoryKey, category]) => {
      // Regular questions
      category.questions.forEach((_, index) => {
        allQuestions.push(`${categoryKey}_${index}`);
      });
      
      // Reverse questions
      if (category.reverseQuestions) {
        category.reverseQuestions.forEach((_, index) => {
          allQuestions.push(`${categoryKey}_reverse_${index}`);
        });
      }
    });

    const answered = allQuestions.filter(questionKey => answers[questionKey] !== undefined).length;
    return { answered, total: allQuestions.length };
  };

  // Navigate to category with smooth animation
  const navigateToCategory = (categoryKey) => {
    const categories = Object.keys(assessmentData.categories);
    const categoryIndex = categories.findIndex(key => key === categoryKey);

    if (categoryIndex !== -1) {
      // Add smooth transition effect
      setCurrentPage(categoryIndex);

      // Reset question index when changing category
      if (onNavigateToQuestion) {
        onNavigateToQuestion(0);
      }

      // Smooth scroll to top of content area
      setTimeout(() => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }, 100);
    }
  };

  // Navigate to specific phase
  const navigateToPhase = (phaseStep) => {
    if (onNavigateToPhase && phaseStep !== currentStep) {
      onNavigateToPhase(phaseStep);
    }
  };

  const totalProgress = getTotalProgress();

  return (
    <div className="hidden lg:block fixed right-0 top-0 h-full w-72 bg-white border-l border-gray-200 overflow-y-auto z-20">
      <div className="p-3 h-full flex flex-col">


        {/* Phase Structure */}
        <div className="flex-1">
          <h3 className="text-base font-semibold text-gray-900 mb-3">
            Assessment Structure
          </h3>

          <div className="space-y-2">
            {assessmentPhases.map((phase) => {
              const isCurrentPhase = phase.step === currentStep;
              // Show actual progress for current phase, total questions for others
              const phaseProgress = isCurrentPhase ? totalProgress : { answered: 0, total: phase.totalQuestions };

              return (
                <div key={phase.id} className={`border p-3 transition-all duration-300 ease-in-out ${isCurrentPhase ? 'border-gray-400 bg-gray-50 shadow-sm' : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm'}`}>
                  <button
                    onClick={() => navigateToPhase(phase.step)}
                    className={`w-full text-left transition-all duration-200`}
                    disabled={isCurrentPhase}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <h4 className={`text-sm font-semibold ${isCurrentPhase ? 'text-gray-900' : 'text-gray-700 hover:text-gray-900'}`}>
                          {phase.title}
                        </h4>
                        <p className={`text-xs ${isCurrentPhase ? 'text-gray-700' : 'text-gray-600 hover:text-gray-700'}`}>
                          {phase.subtitle}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className={`text-xs font-medium ${isCurrentPhase ? 'text-gray-900' : 'text-gray-500'}`}>
                          {phaseProgress.answered}/{phaseProgress.total}
                        </div>
                        {!isCurrentPhase && (
                          <ChevronRight className="h-3 w-3 text-gray-500" />
                        )}
                      </div>
                    </div>
                  </button>

                  {/* Categories for current phase with smooth slide animation */}
                  <div className={`transition-all duration-300 ease-in-out overflow-hidden ${
                    isCurrentPhase ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'
                  }`}>
                    {isCurrentPhase && (
                      <div className="mt-2 space-y-1">
                        {Object.entries(assessmentData.categories).map(([categoryKey, category], categoryIndex) => {
                        const categoryProgress = getCategoryProgress(categoryKey);
                        const isCompleted = categoryProgress.answered === categoryProgress.total;
                        const isCurrentCategory = categoryIndex === currentPage;

                        return (
                          <div key={categoryKey}>
                            <button
                              onClick={() => navigateToCategory(categoryKey)}
                              className={`w-full text-left p-3 border transition-all duration-300 ease-in-out ${
                                isCurrentCategory
                                  ? 'bg-gray-900 border-gray-900 text-white transform scale-[1.02]'
                                  : isCompleted
                                  ? 'bg-gray-100 border-gray-300 text-gray-900'
                                  : categoryProgress.answered > 0
                                  ? 'bg-gray-50 border-gray-300 text-gray-800'
                                  : 'bg-white border-gray-200 text-gray-600 hover:bg-gray-50 hover:border-gray-300'
                              }`}
                            >
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium">
                                  {category.name}
                                </span>
                                <div className="flex items-center space-x-2">
                                  <span className="text-xs font-medium">
                                    {categoryProgress.answered}/{categoryProgress.total}
                                  </span>
                                  <ChevronRight className="h-3 w-3" />
                                </div>
                              </div>
                            </button>

                            {/* Quick Navigate Buttons for current category with smooth animation */}
                            <div className={`transition-all duration-300 ease-in-out overflow-hidden ${
                              isCurrentCategory ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                            }`}>
                              {isCurrentCategory && onNavigateToQuestion && (
                                <QuickNavigateButtons
                                  categoryKey={categoryKey}
                                  categoryData={category}
                                  answers={answers}
                                  currentQuestionIndex={currentQuestionIndex}
                                  onNavigateToQuestion={onNavigateToQuestion}
                                />
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>



        {/* Total Progress - Bottom Section */}
        <div className="mt-auto pt-4 border-t border-gray-200">
          <div className="text-center mb-3">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Current Phase Progress</h4>
            <div className="w-full bg-gray-200 h-2">
              <div
                className="bg-gray-900 h-2 transition-all duration-500 ease-out"
                style={{ width: `${totalProgress.total > 0 ? (totalProgress.answered / totalProgress.total) * 100 : 0}%` }}
              ></div>
            </div>
            <div className="text-xs text-gray-600 mt-1 font-medium">
              {totalProgress.total > 0 ? Math.round((totalProgress.answered / totalProgress.total) * 100) : 0}% Complete
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssessmentSidebar;
